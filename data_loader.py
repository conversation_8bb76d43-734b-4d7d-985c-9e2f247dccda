import pandas as pd
import os
from typing import List, Dict
from models import Chapter, LearningCard, PracticeQuestion, ChapterContent, Subject

class DataLoader:
    def __init__(self, books_path: str = None):
        # Always use books directory from current working directory
        if books_path is None:
            self.books_path = os.path.join(os.getcwd(), "books")
        else:
            self.books_path = books_path
    
    def get_available_subjects(self) -> List[str]:
        """Get list of available subjects from books directory"""
        subjects = []
        for item in os.listdir(self.books_path):
            item_path = os.path.join(self.books_path, item)
            if os.path.isdir(item_path):
                subjects.append(item)
        return subjects
    
    def load_subject(self, subject_name: str) -> Subject:
        """Load all data for a specific subject"""
        subject_path = os.path.join(self.books_path, subject_name)

        # Load chapters from subject directory
        chapters = self._load_chapters(subject_path)

        # Load learning cards from subject directory
        learning_cards = self._load_learning_cards(subject_path)

        # Load practice questions from subject directory
        practice_questions = self._load_practice_questions(subject_path)

        # Load chapter contents from subject directory
        chapter_contents = self._load_chapter_contents(subject_path)

        return Subject(
            name=subject_name,
            path=subject_path,
            chapters=chapters,
            learning_cards=learning_cards,
            practice_questions=practice_questions,
            chapter_contents=chapter_contents
        )
    
    def _load_chapters(self) -> List[Chapter]:
        """Load chapters from chapters.xlsx"""
        chapters_file = os.path.join(self.books_path, "chapters.xlsx")
        if not os.path.exists(chapters_file):
            return []
        
        df = pd.read_excel(chapters_file)
        chapters = []
        
        for _, row in df.iterrows():
            chapter = Chapter(
                id=str(row.iloc[0]),  # First column: id
                name=str(row.iloc[1])  # Second column: name
            )
            chapters.append(chapter)
        
        return chapters
    
    def _load_learning_cards(self) -> List[LearningCard]:
        """Load learning cards from learning.xlsx"""
        learning_file = os.path.join(self.books_path, "learning.xlsx")
        if not os.path.exists(learning_file):
            return []

        df = pd.read_excel(learning_file)
        learning_cards = []

        for _, row in df.iterrows():
            # Handle NaN values
            chapter = str(row.iloc[0]) if pd.notna(row.iloc[0]) else ""
            type_val = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""
            question = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""
            question_hu = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""
            answer = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""
            answer_hu = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""

            # Skip rows with missing essential data
            if not chapter or not question or not answer:
                continue

            card = LearningCard(
                chapter=chapter,
                type=type_val,
                question=question,
                question_hu=question_hu,
                answer=answer,
                answer_hu=answer_hu
            )
            learning_cards.append(card)

        return learning_cards
    
    def _load_practice_questions(self) -> List[PracticeQuestion]:
        """Load practice questions from practice.xlsx"""
        practice_file = os.path.join(self.books_path, "practice.xlsx")
        if not os.path.exists(practice_file):
            return []

        df = pd.read_excel(practice_file)
        practice_questions = []

        for _, row in df.iterrows():
            # Handle NaN values and ensure we have enough columns
            if len(row) < 11:
                continue

            chapter = str(row.iloc[0]) if pd.notna(row.iloc[0]) else ""
            question = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""
            option1 = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""
            option1_hu = str(row.iloc[3]) if pd.notna(row.iloc[3]) else ""
            option2 = str(row.iloc[4]) if pd.notna(row.iloc[4]) else ""
            option2_hu = str(row.iloc[5]) if pd.notna(row.iloc[5]) else ""
            option3 = str(row.iloc[6]) if pd.notna(row.iloc[6]) else ""
            option3_hu = str(row.iloc[7]) if pd.notna(row.iloc[7]) else ""
            option4 = str(row.iloc[8]) if pd.notna(row.iloc[8]) else ""
            option4_hu = str(row.iloc[9]) if pd.notna(row.iloc[9]) else ""
            answer = str(row.iloc[10]) if pd.notna(row.iloc[10]) else ""

            # Skip rows with missing essential data
            if not chapter or not question or not answer:
                continue

            question_obj = PracticeQuestion(
                chapter=chapter,
                question=question,
                option1=option1,
                option1_hu=option1_hu,
                option2=option2,
                option2_hu=option2_hu,
                option3=option3,
                option3_hu=option3_hu,
                option4=option4,
                option4_hu=option4_hu,
                answer=answer
            )
            practice_questions.append(question_obj)

        return practice_questions
    
    def _load_chapter_contents(self, subject_path: str) -> List[ChapterContent]:
        """Load chapter contents from DATA folder"""
        data_path = os.path.join(subject_path, "DATA")
        if not os.path.exists(data_path):
            return []
        
        chapter_contents = []
        
        for filename in os.listdir(data_path):
            if filename.endswith('.txt'):
                chapter_id = filename[:-4]  # Remove .txt extension
                file_path = os.path.join(data_path, filename)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse EN and HU sections while preserving line breaks
                parts = content.split('HU:')
                content_en = parts[0].replace('EN:', '').strip()
                content_hu = parts[1].strip() if len(parts) > 1 else ""

                # Preserve line breaks by replacing them with HTML line breaks for display
                # but keep original formatting for text display
                content_en = content_en.replace('\r\n', '\n').replace('\r', '\n')
                content_hu = content_hu.replace('\r\n', '\n').replace('\r', '\n')

                # Format the first line as title (bold)
                content_en = self._format_content_with_title(content_en)
                content_hu = self._format_content_with_title(content_hu)
                
                chapter_content = ChapterContent(
                    chapter=chapter_id,
                    content_en=content_en,
                    content_hu=content_hu
                )
                chapter_contents.append(chapter_content)

        return chapter_contents

    def _format_content_with_title(self, content: str) -> str:
        """Format content with the first line as bold title"""
        if not content.strip():
            return content

        lines = content.split('\n')
        if len(lines) == 0:
            return content

        # Get the first non-empty line as title
        title_line = ""
        content_lines = []
        title_found = False

        for line in lines:
            if not title_found and line.strip():
                title_line = line.strip()
                title_found = True
            elif title_found:
                content_lines.append(line)

        if title_line:
            # Directly generate HTML with proper escaping
            import html
            escaped_title = html.escape(title_line)
            escaped_content = html.escape('\n'.join(content_lines))
            formatted_content = f"<strong>{escaped_title}</strong>\n\n{escaped_content}"
            return formatted_content

        return content
